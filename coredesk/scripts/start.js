#!/usr/bin/env node

/**
 * CoreDesk Framework - Startup Script
 * Configures environment for optimal Electron execution in different platforms
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

class CoreDeskStarter {
    constructor() {
        this.platform = os.platform();
        this.isWSL = this.detectWSL();
        this.electronPath = this.findElectronPath();
    }

    /**
     * Detecta si estamos ejecutando en WSL
     */
    detectWSL() {
        try {
            // Método 1: Verificar /proc/version
            if (fs.existsSync('/proc/version')) {
                const version = fs.readFileSync('/proc/version', 'utf8');
                if (version.toLowerCase().includes('microsoft') || 
                    version.toLowerCase().includes('wsl')) {
                    return true;
                }
            }

            // Método 2: Verificar variable de entorno WSL
            if (process.env.WSL_DISTRO_NAME || process.env.WSLENV) {
                return true;
            }

            // Método 3: Verificar si existe /mnt/c (típico de WSL)
            if (fs.existsSync('/mnt/c')) {
                return true;
            }

            return false;
        } catch (error) {
            console.log('[CoreDesk] WSL detection failed, assuming native Linux');
            return false;
        }
    }

    /**
     * Encuentra la ruta de Electron
     */
    findElectronPath() {
        const possiblePaths = [
            path.join(__dirname, '..', 'node_modules', '.bin', 'electron'),
            path.join(__dirname, '..', 'node_modules', 'electron', 'dist', 'electron'),
        ];

        for (const electronPath of possiblePaths) {
            if (fs.existsSync(electronPath)) {
                return electronPath;
            }
        }

        // Usar node para ejecutar electron como módulo
        return 'node';
    }

    /**
     * Configura variables de entorno para WSL2
     */
    setupWSLEnvironment() {
        const env = { ...process.env };

        // Configurar directorio temporal
        const tempDir = path.join(os.homedir(), '.coredesk-temp');
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }

        // Variables de entorno para WSL2
        env.TMPDIR = tempDir;
        env.XDG_RUNTIME_DIR = tempDir;
        env.ELECTRON_DISABLE_SANDBOX = '1';
        env.ELECTRON_DISABLE_GPU_SANDBOX = '1';
        env.ELECTRON_NO_ATTACH_CONSOLE = '1';

        // Suprimir errores de memoria compartida
        env.ELECTRON_DISABLE_SHARED_MEMORY = '1';
        env.CHROMIUM_FLAGS = '--disable-dev-shm-usage --no-sandbox --disable-gpu-sandbox';

        // Configurar display para WSL2 con múltiples opciones
        if (!env.DISPLAY) {
            // Intentar diferentes configuraciones de display
            const possibleDisplays = [':0', ':1', 'localhost:0.0'];
            env.DISPLAY = possibleDisplays[0];
        }

        // Configuraciones adicionales para WSL2
        env.LIBGL_ALWAYS_INDIRECT = '1';

        // Configurar WSLg si está disponible
        if (fs.existsSync('/mnt/wslg')) {
            env.WAYLAND_DISPLAY = 'wayland-0';
            env.XDG_RUNTIME_DIR = '/mnt/wslg/runtime-dir';
            env.PULSE_RUNTIME_PATH = '/mnt/wslg/runtime-dir';
        } else {
            env.XDG_RUNTIME_DIR = tempDir;
        }

        return env;
    }

    /**
     * Configura variables de entorno para Linux nativo
     */
    setupLinuxEnvironment() {
        const env = { ...process.env };
        
        // Configuraciones básicas para Linux
        env.ELECTRON_DISABLE_SANDBOX = '1';
        
        return env;
    }

    /**
     * Verifica si X11 está disponible
     */
    async checkX11() {
        if (!this.isWSL) return true;

        try {
            // Verificar si WSLg está disponible (Windows 11)
            if (fs.existsSync('/mnt/wslg')) {
                console.log('✅ [CoreDesk] WSLg detectado - soporte gráfico nativo disponible');
                return true;
            }

            // Verificar si hay un servidor X11 ejecutándose
            const { spawn } = require('child_process');
            const xdpyinfo = spawn('xdpyinfo', ['-display', process.env.DISPLAY || ':0']);

            return new Promise((resolve) => {
                xdpyinfo.on('close', (code) => {
                    if (code === 0) {
                        console.log('✅ [CoreDesk] Servidor X11 detectado');
                        resolve(true);
                    } else {
                        console.log('⚠️  [CoreDesk] No se detectó servidor X11');
                        resolve(false);
                    }
                });

                xdpyinfo.on('error', () => {
                    console.log('⚠️  [CoreDesk] xdpyinfo no disponible');
                    resolve(false);
                });
            });
        } catch (error) {
            console.log('⚠️  [CoreDesk] Error verificando X11:', error.message);
            return false;
        }
    }

    /**
     * Inicia la aplicación Electron
     */
    async start() {
        console.log('🚀 [CoreDesk] Iniciando CoreDesk Framework...');
        console.log(`📋 [CoreDesk] Plataforma: ${this.platform}`);
        console.log(`🔧 [CoreDesk] WSL detectado: ${this.isWSL ? 'Sí' : 'No'}`);

        // Verificar X11 en WSL2
        if (this.isWSL) {
            const x11Available = await this.checkX11();
            if (!x11Available) {
                console.log('');
                console.log('❌ [CoreDesk] PROBLEMA: No se detectó servidor X11');
                console.log('');
                console.log('📋 [CoreDesk] Para usar aplicaciones gráficas en WSL2, necesitas:');
                console.log('   1. Windows 11 con WSLg (recomendado)');
                console.log('   2. O instalar un servidor X11 como VcXsrv o Xming');
                console.log('');
                console.log('🔧 [CoreDesk] Instrucciones:');
                console.log('   • Windows 11: Actualiza WSL2 con "wsl --update"');
                console.log('   • Windows 10: Instala VcXsrv y configura DISPLAY');
                console.log('');
                console.log('⚠️  [CoreDesk] La aplicación continuará pero puede no ser visible');
                console.log('');
            }
        }

        let env;
        if (this.isWSL) {
            console.log('⚙️  [CoreDesk] Configurando entorno para WSL2...');
            env = this.setupWSLEnvironment();
        } else {
            console.log('⚙️  [CoreDesk] Configurando entorno para Linux nativo...');
            env = this.setupLinuxEnvironment();
        }

        const mainPath = path.join(__dirname, '..', 'main.js');

        // Usar npx para ejecutar electron
        const command = 'npx';
        const args = ['electron', mainPath];

        console.log(`🔌 [CoreDesk] Ejecutando: ${command} ${args.join(' ')}`);
        console.log('📱 [CoreDesk] Iniciando aplicación...');

        const electronProcess = spawn(command, args, {
            env,
            stdio: ['inherit', 'inherit', 'pipe'], // Redirigir stderr para filtrar errores
            cwd: path.join(__dirname, '..')
        });

        // Filtrar errores de memoria compartida en WSL2
        if (this.isWSL && electronProcess.stderr) {
            electronProcess.stderr.on('data', (data) => {
                const errorText = data.toString();
                
                // Filtrar errores conocidos de WSL2
                const filteredErrors = [
                    'platform_shared_memory_region_posix.cc',
                    'Creating shared memory',
                    'Unable to access(W_OK|X_OK) /tmp',
                    'Failed to connect to the bus'
                ];

                const shouldFilter = filteredErrors.some(filter => 
                    errorText.includes(filter)
                );

                if (!shouldFilter) {
                    process.stderr.write(data);
                }
            });
        }

        electronProcess.on('close', (code) => {
            if (code === 0) {
                console.log('✅ [CoreDesk] Aplicación cerrada correctamente');
            } else {
                console.log(`❌ [CoreDesk] Aplicación cerrada con código: ${code}`);
            }
            process.exit(code);
        });

        electronProcess.on('error', (error) => {
            console.error('❌ [CoreDesk] Error al iniciar la aplicación:', error.message);
            process.exit(1);
        });

        // Manejar señales del sistema
        process.on('SIGINT', () => {
            console.log('\n🛑 [CoreDesk] Cerrando aplicación...');
            electronProcess.kill('SIGINT');
        });

        process.on('SIGTERM', () => {
            console.log('\n🛑 [CoreDesk] Terminando aplicación...');
            electronProcess.kill('SIGTERM');
        });
    }
}

// Ejecutar el starter
if (require.main === module) {
    const starter = new CoreDeskStarter();
    starter.start().catch(error => {
        console.error('❌ [CoreDesk] Error fatal:', error.message);
        process.exit(1);
    });
}

module.exports = CoreDeskStarter;
